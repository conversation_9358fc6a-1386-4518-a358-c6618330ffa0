import PyPDF2
import fitz  # PyMuPDF
import os

def extract_images_from_pdf(pdf_path):
    """Extract images from PDF using PyMuPDF"""
    try:
        # Try with PyMuPDF first
        doc = fitz.open(pdf_path)
        image_count = 0
        
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            image_list = page.get_images()
            
            print(f"Page {page_num + 1}: Found {len(image_list)} images")
            
            for img_index, img in enumerate(image_list):
                xref = img[0]
                pix = fitz.Pixmap(doc, xref)
                
                if pix.n - pix.alpha < 4:  # GRAY or RGB
                    image_filename = f"image_page{page_num + 1}_{img_index + 1}.png"
                    pix.save(image_filename)
                    print(f"Saved: {image_filename}")
                    image_count += 1
                else:  # CMYK: convert to RGB first
                    pix1 = fitz.Pixmap(fitz.csRGB, pix)
                    image_filename = f"image_page{page_num + 1}_{img_index + 1}.png"
                    pix1.save(image_filename)
                    print(f"Saved: {image_filename}")
                    image_count += 1
                    pix1 = None
                
                pix = None
        
        doc.close()
        return image_count
        
    except ImportError:
        print("PyMuPDF not available. Installing...")
        import subprocess
        import sys
        subprocess.check_call([sys.executable, "-m", "pip", "install", "PyMuPDF"])
        
        # Try again after installation
        import fitz
        return extract_images_from_pdf(pdf_path)
        
    except Exception as e:
        print(f"Error extracting images: {e}")
        return 0

def check_pdf_structure(pdf_path):
    """Check PDF structure for images and diagrams"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                
                # Check if page has images
                if '/XObject' in page['/Resources']:
                    xobjects = page['/Resources']['/XObject'].get_object()
                    for obj in xobjects:
                        if xobjects[obj]['/Subtype'] == '/Image':
                            print(f"Page {page_num + 1}: Found image object {obj}")
                
                # Extract text to look for diagram references
                text = page.extract_text()
                if 'diagram' in text.lower() or 'apparatus' in text.lower() or 'shown below' in text.lower():
                    print(f"Page {page_num + 1}: Contains diagram references")
                    
    except Exception as e:
        print(f"Error checking PDF structure: {e}")

if __name__ == "__main__":
    pdf_file = "Q-Pdf-Grade 7 Integrated Science End of Term 2 Examination 2024.pdf"
    
    print("Checking PDF structure...")
    check_pdf_structure(pdf_file)
    
    print("\nExtracting images...")
    image_count = extract_images_from_pdf(pdf_file)
    
    if image_count > 0:
        print(f"\nSuccessfully extracted {image_count} images!")
        print("Images saved as: image_page[X]_[Y].png")
    else:
        print("\nNo images found or extraction failed.")
        print("The diagrams might be:")
        print("1. Vector graphics (not raster images)")
        print("2. Embedded as text/shapes")
        print("3. Part of the page layout")
