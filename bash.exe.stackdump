Stack trace:
Frame         Function      Args
0007FFFF9890  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFF9890, 0007FFFF8790) msys-2.0.dll+0x1FEBA
0007FFFF9890  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9B68) msys-2.0.dll+0x67F9
0007FFFF9890  000210046832 (000210285FF9, 0007FFFF9748, 0007FFFF9890, 000000000000) msys-2.0.dll+0x6832
0007FFFF9890  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9890  0002100690B4 (0007FFFF98A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9B70  00021006A49D (0007FFFF98A0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFE18E00000 ntdll.dll
7FFE18860000 KERNEL32.DLL
7FFE16080000 KERNELBASE.dll
7FFE17AE0000 USER32.dll
7FFE169E0000 win32u.dll
7FFE17CB0000 GDI32.dll
7FFE16480000 gdi32full.dll
7FFE16930000 msvcp_win.dll
7FFE15F30000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFE189F0000 advapi32.dll
7FFE17620000 msvcrt.dll
7FFE18AC0000 sechost.dll
7FFE17780000 RPCRT4.dll
7FFE15420000 CRYPTBASE.DLL
7FFE16740000 bcryptPrimitives.dll
7FFE18490000 IMM32.DLL
