import subprocess
import sys
import os

def install_package(package):
    """Install a package using pip"""
    subprocess.check_call([sys.executable, "-m", "pip", "install", package])

def convert_html_to_pdf():
    try:
        # Try to import weasyprint
        import weasyprint
        print("WeasyPrint is available")
        
        # Convert HTML to PDF
        html_file = "ST_MARYS_RUARAKA_HOLIDAY_HOMEWORK.html"
        pdf_file = "ST_MARYS_RUARAKA_HOLIDAY_HOMEWORK.pdf"
        
        # Read HTML content
        with open(html_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # Convert to PDF
        weasyprint.HTML(string=html_content).write_pdf(pdf_file)
        print(f"Successfully converted {html_file} to {pdf_file}")
        
    except ImportError:
        print("WeasyPrint not found. Installing...")
        try:
            install_package("weasyprint")
            import weasyprint
            
            # Convert HTML to PDF
            html_file = "ST_MARYS_RUARAKA_HOLIDAY_HOMEWORK.html"
            pdf_file = "ST_MARYS_RUARAKA_HOLIDAY_HOMEWORK.pdf"
            
            with open(html_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            weasyprint.HTML(string=html_content).write_pdf(pdf_file)
            print(f"Successfully converted {html_file} to {pdf_file}")
            
        except Exception as e:
            print(f"Error installing or using WeasyPrint: {e}")
            print("Alternative: You can open the HTML file in a browser and print to PDF")
            return False
    
    except Exception as e:
        print(f"Error converting to PDF: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = convert_html_to_pdf()
    if success:
        print("\nPDF conversion completed successfully!")
        print("Files created:")
        print("- ST_MARYS_RUARAKA_HOLIDAY_HOMEWORK.html (HTML version)")
        print("- ST_MARYS_RUARAKA_HOLIDAY_HOMEWORK.pdf (PDF version)")
    else:
        print("\nHTML file created successfully!")
        print("You can open ST_MARYS_RUARAKA_HOLIDAY_HOMEWORK.html in a browser and print to PDF")
