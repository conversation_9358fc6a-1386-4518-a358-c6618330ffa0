import fitz  # PyMuPDF
import os

def extract_images_from_pdf(pdf_path):
    """Extract images from PDF using PyMuPDF"""
    try:
        doc = fitz.open(pdf_path)
        image_count = 0
        
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            image_list = page.get_images()
            
            print(f"Page {page_num + 1}: Found {len(image_list)} images")
            
            for img_index, img in enumerate(image_list):
                try:
                    xref = img[0]
                    pix = fitz.Pixmap(doc, xref)
                    
                    if pix.n - pix.alpha < 4:  # GRAY or RGB
                        image_filename = f"image_page{page_num + 1}_{img_index + 1}.png"
                        pix.save(image_filename)
                        print(f"Saved: {image_filename}")
                        image_count += 1
                    else:  # CMYK: convert to RGB first
                        pix1 = fitz.Pixmap(fitz.csRGB, pix)
                        image_filename = f"image_page{page_num + 1}_{img_index + 1}.png"
                        pix1.save(image_filename)
                        print(f"Saved: {image_filename}")
                        image_count += 1
                        pix1 = None
                    
                    pix = None
                except Exception as e:
                    print(f"Error extracting image {img_index + 1} from page {page_num + 1}: {e}")
        
        doc.close()
        return image_count
        
    except Exception as e:
        print(f"Error extracting images: {e}")
        return 0

def convert_pdf_pages_to_images(pdf_path):
    """Convert PDF pages to images to capture all visual content"""
    try:
        doc = fitz.open(pdf_path)
        
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            
            # Render page to image
            mat = fitz.Matrix(2, 2)  # 2x zoom for better quality
            pix = page.get_pixmap(matrix=mat)
            
            image_filename = f"page_{page_num + 1}_full.png"
            pix.save(image_filename)
            print(f"Saved full page: {image_filename}")
            
            pix = None
        
        doc.close()
        return len(doc)
        
    except Exception as e:
        print(f"Error converting pages to images: {e}")
        return 0

if __name__ == "__main__":
    pdf_file = "Q-Pdf-Grade 7 Integrated Science End of Term 2 Examination 2024.pdf"
    
    print("Extracting individual images...")
    image_count = extract_images_from_pdf(pdf_file)
    
    print(f"\nExtracted {image_count} individual images")
    
    print("\nConverting full pages to images...")
    page_count = convert_pdf_pages_to_images(pdf_file)
    
    print(f"\nConverted {page_count} pages to images")
    
    # List all created image files
    image_files = [f for f in os.listdir('.') if f.endswith('.png')]
    if image_files:
        print(f"\nCreated image files:")
        for img_file in sorted(image_files):
            print(f"- {img_file}")
    else:
        print("\nNo image files were created.")
