import PyPDF2
import sys

def extract_pdf_text(pdf_path):
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text = ""
            
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                text += f"\n--- Page {page_num + 1} ---\n"
                text += page.extract_text()
                text += "\n"
            
            return text
    except Exception as e:
        return f"Error extracting PDF: {str(e)}"

if __name__ == "__main__":
    pdf_file = "Q-Pdf-Grade 7 Integrated Science End of Term 2 Examination 2024.pdf"
    extracted_text = extract_pdf_text(pdf_file)
    
    # Save to text file
    with open("extracted_content.txt", "w", encoding="utf-8") as f:
        f.write(extracted_text)
    
    print("PDF content extracted to extracted_content.txt")
    print("\nFirst 500 characters:")
    print(extracted_text[:500])
